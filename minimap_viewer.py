#!/usr/bin/env python3
"""
Minimap Viewer Component for FiendishFinder

A PyQt6-based minimap viewer with zoom functionality, floor navigation,
and camera position preservation for Tibia minimap exploration.
"""

import sys
import math
import json
import uuid
import time
from pathlib import Path
from typing import Dict, Optional, List, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import logging

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QGraphicsView, QGraphicsScene, QGraphicsPixmapItem, QPushButton,
    QComboBox, QLabel, QSlider, QFrame, QSizePolicy, QMessageBox,
    QGraphicsLineItem, QGraphicsRectItem, QGraphicsPolygonItem,
    QGraphicsItem, QCheckBox, QColorDialog, QLineEdit, QFormLayout,
    QScrollArea, QGroupBox
)
from PyQt6.QtCore import Qt, QRectF, pyqtSignal, QPointF
from PyQt6.QtGui import QPixmap, QWheelEvent, QMouseEvent, QPainter, QKeyEvent, QPen, QColor, QBrush, QPolygon, QPolygonF

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MonsterDifficulty(Enum):
    """Monster difficulty levels for area classification."""
    HARMLESS = "harmless"
    TRIVIAL = "trivial"
    EASY = "easy"
    MEDIUM = "medium"
    HARD = "hard"
    CHALLENGING = "challenging"


@dataclass
class AreaData:
    """Data structure for storing area information."""
    area_id: str
    floor: int
    name: str
    coordinates: List[Tuple[float, float]]  # List of (x, y) points for polygon
    area_type: str  # "rectangle" or "polygon"
    difficulty_levels: List[str]  # List of MonsterDifficulty values
    color: str  # Hex color string
    transparency: float  # 0.0 to 1.0
    metadata: Dict[str, Any]  # Additional custom fields
    created_timestamp: float
    modified_timestamp: float

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AreaData':
        """Create AreaData from dictionary."""
        return cls(**data)


class AreaDataManager:
    """Manages area data persistence and storage."""

    def __init__(self, data_dir: str = "area_data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        self.areas_file = self.data_dir / "areas.json"
        self.areas: Dict[int, List[AreaData]] = {}  # floor -> list of areas
        self.load_areas()

    def load_areas(self) -> None:
        """Load areas from JSON file."""
        if not self.areas_file.exists():
            logger.info("No existing area data found, starting with empty areas")
            return

        try:
            with open(self.areas_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            self.areas = {}
            for floor_str, areas_list in data.items():
                floor = int(floor_str)
                self.areas[floor] = [AreaData.from_dict(area_dict) for area_dict in areas_list]

            total_areas = sum(len(areas) for areas in self.areas.values())
            logger.info(f"Loaded {total_areas} areas across {len(self.areas)} floors")

        except Exception as e:
            logger.error(f"Failed to load area data: {e}")
            self.areas = {}

    def save_areas(self) -> None:
        """Save areas to JSON file."""
        try:
            data = {}
            for floor, areas_list in self.areas.items():
                data[str(floor)] = [area.to_dict() for area in areas_list]

            with open(self.areas_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            total_areas = sum(len(areas) for areas in self.areas.values())
            logger.info(f"Saved {total_areas} areas to {self.areas_file}")

        except Exception as e:
            logger.error(f"Failed to save area data: {e}")

    def add_area(self, area: AreaData) -> None:
        """Add a new area."""
        if area.floor not in self.areas:
            self.areas[area.floor] = []
        self.areas[area.floor].append(area)
        self.save_areas()

    def remove_area(self, floor: int, area_id: str) -> bool:
        """Remove an area by ID."""
        if floor not in self.areas:
            return False

        original_count = len(self.areas[floor])
        self.areas[floor] = [area for area in self.areas[floor] if area.area_id != area_id]

        if len(self.areas[floor]) < original_count:
            self.save_areas()
            return True
        return False

    def update_area(self, area: AreaData) -> bool:
        """Update an existing area."""
        if area.floor not in self.areas:
            return False

        for i, existing_area in enumerate(self.areas[area.floor]):
            if existing_area.area_id == area.area_id:
                self.areas[area.floor][i] = area
                self.save_areas()
                return True
        return False

    def get_areas_for_floor(self, floor: int) -> List[AreaData]:
        """Get all areas for a specific floor."""
        return self.areas.get(floor, [])

    def get_area_by_id(self, floor: int, area_id: str) -> Optional[AreaData]:
        """Get a specific area by ID."""
        for area in self.get_areas_for_floor(floor):
            if area.area_id == area_id:
                return area
        return None

    def get_default_colors(self) -> Dict[str, str]:
        """Get default colors for each difficulty level."""
        return {
            MonsterDifficulty.HARMLESS.value: "#00FF00",    # Green
            MonsterDifficulty.TRIVIAL.value: "#7FFF00",     # Chartreuse
            MonsterDifficulty.EASY.value: "#FFFF00",        # Yellow
            MonsterDifficulty.MEDIUM.value: "#FFA500",      # Orange
            MonsterDifficulty.HARD.value: "#FF4500",        # Red-Orange
            MonsterDifficulty.CHALLENGING.value: "#FF0000", # Red
        }


class AreaGraphicsItem(QGraphicsPolygonItem):
    """Graphics item for rendering areas on the minimap."""

    def __init__(self, area_data: AreaData, parent=None):
        super().__init__(parent)
        self.area_data = area_data
        self.is_selected_area = False
        self.hover_effect = False

        # Set up the polygon from coordinates
        polygon = QPolygonF()
        for x, y in area_data.coordinates:
            polygon.append(QPointF(x, y))
        self.setPolygon(polygon)

        # Set z-value to render above map and crosshair ranges but below crosshair lines
        self.setZValue(998.5)

        # Enable hover and selection
        self.setAcceptHoverEvents(True)
        self.setFlag(QGraphicsItem.GraphicsItemFlag.ItemIsSelectable, True)

        self.update_appearance()

    def update_appearance(self):
        """Update the visual appearance based on area data and state."""
        color = QColor(self.area_data.color)

        # Adjust transparency
        alpha = int(self.area_data.transparency * 255)
        color.setAlpha(alpha)

        # Create brush for fill
        brush = QBrush(color)
        self.setBrush(brush)

        # Create pen for outline
        outline_color = QColor(self.area_data.color)
        outline_color.setAlpha(min(255, alpha + 100))  # Make outline more opaque

        pen_width = 2.0 if self.is_selected_area else 1.0
        if self.hover_effect:
            pen_width += 1.0

        pen = QPen(outline_color)
        pen.setWidthF(pen_width)
        pen.setCosmetic(True)  # Keep consistent width regardless of zoom
        self.setPen(pen)

    def set_selected_area(self, selected: bool):
        """Set the selection state of this area."""
        self.is_selected_area = selected
        self.update_appearance()

    def hoverEnterEvent(self, event):
        """Handle mouse hover enter."""
        self.hover_effect = True
        self.update_appearance()
        super().hoverEnterEvent(event)

    def hoverLeaveEvent(self, event):
        """Handle mouse hover leave."""
        self.hover_effect = False
        self.update_appearance()
        super().hoverLeaveEvent(event)

    def update_from_area_data(self, area_data: AreaData):
        """Update the graphics item from new area data."""
        self.area_data = area_data

        # Update polygon
        polygon = QPolygonF()
        for x, y in area_data.coordinates:
            polygon.append(QPointF(x, y))
        self.setPolygon(polygon)

        self.update_appearance()


class AreaEditingMode(Enum):
    """Area editing modes."""
    DISABLED = "disabled"
    RECTANGLE = "rectangle"
    POLYGON = "polygon"


class MinimapGraphicsView(QGraphicsView):
    """Custom QGraphicsView for minimap display with zoom, pan, crosshair, and area editing capabilities."""

    viewTransformed = pyqtSignal(float, QPointF)
    areaCreated = pyqtSignal(AreaData)
    areaSelected = pyqtSignal(str)  # area_id

    def __init__(self, parent=None):
        super().__init__(parent)

        self.setDragMode(QGraphicsView.DragMode.NoDrag)
        self.setRenderHint(QPainter.RenderHint.Antialiasing)
        self.setRenderHint(QPainter.RenderHint.SmoothPixmapTransform)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        self.zoom_factor = 1.0
        self.min_zoom = 0.1
        self.max_zoom = 20.0
        self.zoom_step = 1.15

        self.pan_enabled = True
        self.last_pan_point = QPointF()
        self.is_panning = False

        self.floor_camera_positions = {}
        self.current_floor_id = None

        self.crosshair_diagonals: List[Optional[QGraphicsLineItem]] = [None] * 8
        self.crosshair_center_square: Optional[QGraphicsRectItem] = None
        self.crosshair_inner_range: Optional[QGraphicsRectItem] = None
        self.crosshair_outer_range: Optional[QGraphicsRectItem] = None
        self.global_crosshair_position: Optional[QPointF] = None

        # Area editing functionality
        self.area_editing_mode = AreaEditingMode.DISABLED
        self.area_graphics_items: Dict[str, AreaGraphicsItem] = {}
        self.selected_area_id: Optional[str] = None

        # Crosshair visibility state for area editing mode
        self.crosshairs_hidden_for_area_editing = False
        self.saved_crosshair_position_for_area_editing: Optional[QPointF] = None

        # Area creation state
        self.creating_area = False
        self.area_creation_points: List[QPointF] = []
        self.temp_area_item: Optional[QGraphicsPolygonItem] = None
        self.area_data_manager = AreaDataManager()
        
    def wheelEvent(self, event: QWheelEvent):
        """Handle mouse wheel events for zooming."""
        zoom_in = event.angleDelta().y() > 0
        self.zoom(zoom_in, event.position())

    def mousePressEvent(self, event: QMouseEvent):
        """Handle mouse press events for panning, crosshair placement, and area editing."""
        mouse_pos = event.position()
        mouse_point = QPointF(mouse_pos.x(), mouse_pos.y())
        scene_pos = self.mapToScene(mouse_point.toPoint())

        # Handle middle mouse button panning (works regardless of area editing mode)
        if event.button() == Qt.MouseButton.MiddleButton:
            self.is_panning = True
            self.last_pan_point = event.position()
            self.setCursor(Qt.CursorShape.ClosedHandCursor)
            return

        # Handle area editing mode
        if self.area_editing_mode != AreaEditingMode.DISABLED:
            if event.button() == Qt.MouseButton.LeftButton:
                # Check if clicking on an existing area first
                item = self.itemAt(mouse_point.toPoint())
                if isinstance(item, AreaGraphicsItem):
                    # Clicking on an existing area - select it
                    self.select_area(item.area_data.area_id)
                    return
                elif self.selected_area_id is not None:
                    # Clicking on empty space while an area is selected - deselect it
                    # Only deselect if not currently creating an area
                    if not self.creating_area:
                        self.deselect_area()
                        return

                # Handle area creation (only if no area was selected and deselected)
                self.handle_area_creation_click(scene_pos)
                return
            elif event.button() == Qt.MouseButton.RightButton:
                self.finish_area_creation()
                return

        # Handle area selection (when not in area editing mode)
        if event.button() == Qt.MouseButton.LeftButton:
            item = self.itemAt(mouse_point.toPoint())
            if isinstance(item, AreaGraphicsItem):
                self.select_area(item.area_data.area_id)
                return
            elif self.selected_area_id is not None:
                # Clicking on empty space while an area is selected - deselect it
                self.deselect_area()
                return

        # Default behavior for panning and crosshairs
        if event.button() == Qt.MouseButton.LeftButton:
            self.is_panning = True
            self.last_pan_point = event.position()
            self.setCursor(Qt.CursorShape.ClosedHandCursor)
        elif event.button() == Qt.MouseButton.RightButton:
            snapped_pos = QPointF(
                math.floor(scene_pos.x()) + 0.5,
                math.floor(scene_pos.y()) + 0.5
            )
            self.place_crosshairs(snapped_pos)
        else:
            super().mousePressEvent(event)
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """Handle mouse move events for panning."""
        if self.is_panning:
            delta = event.position() - self.last_pan_point
            self.last_pan_point = event.position()

            self.horizontalScrollBar().setValue(
                self.horizontalScrollBar().value() - int(delta.x())
            )
            self.verticalScrollBar().setValue(
                self.verticalScrollBar().value() - int(delta.y())
            )
        else:
            super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event: QMouseEvent):
        """Handle mouse release events."""
        if event.button() == Qt.MouseButton.LeftButton or event.button() == Qt.MouseButton.MiddleButton:
            self.is_panning = False
            self.setCursor(Qt.CursorShape.ArrowCursor)
            self.emit_view_transformed()
        else:
            super().mouseReleaseEvent(event)

    def zoom(self, zoom_in: bool, center_point: Optional[QPointF] = None):
        """Zoom the view in or out."""
        factor = self.zoom_step if zoom_in else 1.0 / self.zoom_step
        new_zoom = self.zoom_factor * factor

        if new_zoom < self.min_zoom or new_zoom > self.max_zoom:
            return

        self.zoom_factor = new_zoom

        if center_point:
            self.setTransformationAnchor(QGraphicsView.ViewportAnchor.NoAnchor)
            center_pointf = QPointF(center_point.x(), center_point.y())
            old_pos = self.mapToScene(center_pointf.toPoint())
            self.scale(factor, factor)
            new_pos = self.mapToScene(center_pointf.toPoint())
            delta = new_pos - old_pos
            self.translate(delta.x(), delta.y())
        else:
            self.setTransformationAnchor(QGraphicsView.ViewportAnchor.AnchorViewCenter)
            self.scale(factor, factor)

        self.emit_view_transformed()
        self.update_crosshair_appearance()

    def zoom_to_factor(self, target_zoom: float):
        """Set zoom to a specific factor using absolute transformation."""
        if target_zoom < self.min_zoom or target_zoom > self.max_zoom:
            return

        self.resetTransform()
        self.scale(target_zoom, target_zoom)
        self.zoom_factor = target_zoom
        self.emit_view_transformed()
        self.update_crosshair_appearance()

    def fit_in_view_with_margin(self, margin_percent: float = 0.1):
        """Fit the scene in view with a margin."""
        if self.scene():
            scene_rect = self.scene().itemsBoundingRect()
            if not scene_rect.isEmpty():
                margin_x = scene_rect.width() * margin_percent
                margin_y = scene_rect.height() * margin_percent
                scene_rect.adjust(-margin_x, -margin_y, margin_x, margin_y)

                self.fitInView(scene_rect, Qt.AspectRatioMode.KeepAspectRatio)

                transform = self.transform()
                self.zoom_factor = transform.m11()
                self.emit_view_transformed()
                self.update_crosshair_appearance()

    def save_camera_position(self, floor_id=None):
        """Save current camera position (zoom is global)."""
        if floor_id is None:
            floor_id = self.current_floor_id

        if floor_id is None:
            logger.warning("Cannot save camera position - no floor ID specified")
            return

        viewport_center = self.viewport().rect().center()
        scene_center = self.mapToScene(viewport_center)

        if not scene_center.isNull() and self.scene():
            self.floor_camera_positions[floor_id] = {
                'center': QPointF(scene_center)
            }
        else:
            logger.warning("Could not save camera position - invalid scene coordinates")
    
    def restore_camera_position(self, floor_id=None):
        """Restore previously saved camera position (keep current global zoom)."""
        if floor_id is None:
            floor_id = self.current_floor_id

        if floor_id is None or floor_id not in self.floor_camera_positions:
            logger.info(f"No saved camera position for floor {floor_id}")
            return

        saved_data = self.floor_camera_positions[floor_id]
        saved_center = QPointF(saved_data['center'])

        scene_rect_before = self.scene().sceneRect() if self.scene() else QRectF()
        center_before = self.mapToScene(self.viewport().rect().center())

        viewport_rect = self.viewport().rect()
        target_viewport_center = self.mapFromScene(saved_center)

        current_viewport_center = viewport_rect.center()
        offset_x = target_viewport_center.x() - current_viewport_center.x()
        offset_y = target_viewport_center.y() - current_viewport_center.y()

        h_scroll = self.horizontalScrollBar()
        v_scroll = self.verticalScrollBar()
        h_scroll.setValue(h_scroll.value() + int(offset_x))
        v_scroll.setValue(v_scroll.value() + int(offset_y))

        center_after = self.mapToScene(self.viewport().rect().center())
        logger.info(f"RESTORE Floor {floor_id} - Target: ({saved_center.x():.2f}, {saved_center.y():.2f}), "
                   f"Before: ({center_before.x():.2f}, {center_before.y():.2f}), "
                   f"After: ({center_after.x():.2f}, {center_after.y():.2f}), "
                   f"Scene: {scene_rect_before.width():.0f}x{scene_rect_before.height():.0f}, "
                   f"Zoom: {self.zoom_factor:.4f} (GLOBAL - unchanged)")

        self.emit_view_transformed()
    
    def emit_view_transformed(self):
        """Emit signal when view is transformed."""
        center = self.mapToScene(self.viewport().rect().center())
        self.viewTransformed.emit(self.zoom_factor, center)

    def calculate_crosshair_pen_width(self, base_width: float) -> float:
        """Calculate appropriate pen width based on current zoom level."""
        min_visible_width = 1.0

        if self.zoom_factor >= 1.0:
            adjusted_base = max(base_width * 2.0, min_visible_width)
            return adjusted_base
        else:
            scale_factor = max(1.0 / self.zoom_factor, 1.0)
            scale_factor = min(scale_factor, 8.0)
            scaled_width = base_width * scale_factor
            return max(scaled_width, min_visible_width)

    def get_exiva_diagonal_angles(self) -> List[float]:
        """Get the 8 diagonal boundary angles used by Tibia's Exiva spell."""
        primary_angle_rad = math.atan(1.0 / 2.42)
        primary_angle_deg = math.degrees(primary_angle_rad)

        base_angles = [
            primary_angle_deg,                    # north-northeast (NNE)
            90.0 - primary_angle_deg,            # east-northeast (ENE)
            90.0 + primary_angle_deg,            # east-southeast (ESE)
            180.0 - primary_angle_deg,           # south-southeast (SSE)
            180.0 + primary_angle_deg,           # south-southwest (SSW)
            270.0 - primary_angle_deg,           # west-southwest (WSW)
            270.0 + primary_angle_deg,           # west-northwest (WNW)
            360.0 - primary_angle_deg            # north-northwest (NNW)
        ]

        return base_angles

    def get_crosshair_color(self) -> QColor:
        """Get the appropriate crosshair color based on current floor."""
        if self.current_floor_id == 7:
            return QColor(0, 0, 0, 255)  # Black for floor 07
        else:
            return QColor(255, 255, 255, 255)  # White for all other floors

    def calculate_boundary_line_endpoints(self, center_pos: QPointF, angle_deg: float) -> tuple[QPointF, QPointF]:
        """Calculate the endpoints of a diagonal line that extends to the scene boundaries."""
        if not self.scene():
            angle_rad = math.radians(angle_deg)
            half_length = 100.0
            start_x = center_pos.x() - half_length * math.sin(angle_rad)
            start_y = center_pos.y() + half_length * math.cos(angle_rad)
            end_x = center_pos.x() + half_length * math.sin(angle_rad)
            end_y = center_pos.y() - half_length * math.cos(angle_rad)
            return QPointF(start_x, start_y), QPointF(end_x, end_y)

        scene_rect = self.scene().sceneRect()
        angle_rad = math.radians(angle_deg)
        dx = math.sin(angle_rad)
        dy = -math.cos(angle_rad)

        intersections = []

        if dx != 0:
            t = (scene_rect.left() - center_pos.x()) / dx
            y = center_pos.y() + t * dy
            if scene_rect.top() <= y <= scene_rect.bottom():
                intersections.append(QPointF(scene_rect.left(), y))

        if dx != 0:
            t = (scene_rect.right() - center_pos.x()) / dx
            y = center_pos.y() + t * dy
            if scene_rect.top() <= y <= scene_rect.bottom():
                intersections.append(QPointF(scene_rect.right(), y))

        if dy != 0:
            t = (scene_rect.top() - center_pos.y()) / dy
            x = center_pos.x() + t * dx
            if scene_rect.left() <= x <= scene_rect.right():
                intersections.append(QPointF(x, scene_rect.top()))

        if dy != 0:
            t = (scene_rect.bottom() - center_pos.y()) / dy
            x = center_pos.x() + t * dx
            if scene_rect.left() <= x <= scene_rect.right():
                intersections.append(QPointF(x, scene_rect.bottom()))

        unique_intersections = []
        for point in intersections:
            is_duplicate = False
            for existing in unique_intersections:
                if abs(point.x() - existing.x()) < 0.1 and abs(point.y() - existing.y()) < 0.1:
                    is_duplicate = True
                    break
            if not is_duplicate:
                unique_intersections.append(point)

        if len(unique_intersections) >= 2:
            def get_t_value(point):
                if abs(dx) > abs(dy):
                    return (point.x() - center_pos.x()) / dx if dx != 0 else 0
                else:
                    return (point.y() - center_pos.y()) / dy if dy != 0 else 0

            unique_intersections.sort(key=get_t_value)
            return unique_intersections[0], unique_intersections[-1]

        max_dimension = max(scene_rect.width(), scene_rect.height())
        half_length = max_dimension
        start_x = center_pos.x() - half_length * dx
        start_y = center_pos.y() - half_length * dy
        end_x = center_pos.x() + half_length * dx
        end_y = center_pos.y() + half_length * dy

        return QPointF(start_x, start_y), QPointF(end_x, end_y)

    def calculate_diagonal_line_length(self) -> float:
        """Calculate the length of diagonal lines based on current zoom level."""
        base_length = 100.0

        if self.zoom_factor >= 1.0:
            return base_length * min(2.0, self.zoom_factor)
        else:
            scale_factor = max(1.0, 1.0 / self.zoom_factor)
            scale_factor = min(scale_factor, 10.0)
            return base_length * scale_factor

    def calculate_exiva_range_size(self, range_squares: int) -> float:
        """Calculate the size of an Exiva range rectangle in scene coordinates."""
        return float((range_squares * 2) + 1)

    def place_crosshairs(self, scene_pos: QPointF):
        """Place Exiva-style crosshairs with 8-directional lines and distance ranges."""
        if not self.scene():
            return

        self.remove_crosshairs()
        self.global_crosshair_position = QPointF(scene_pos)

        # If in area editing mode, save position but don't display crosshairs
        if self.area_editing_mode != AreaEditingMode.DISABLED:
            self.saved_crosshair_position_for_area_editing = QPointF(scene_pos)
            self.crosshairs_hidden_for_area_editing = True
            logger.info(f"Crosshair position saved at ({scene_pos.x():.2f}, {scene_pos.y():.2f}) but hidden due to area editing mode")
            return

        crosshair_width = self.calculate_crosshair_pen_width(0.3)
        angles = self.get_exiva_diagonal_angles()

        crosshair_color = self.get_crosshair_color()
        crosshair_pen = QPen(crosshair_color)
        crosshair_pen.setWidthF(crosshair_width)
        crosshair_pen.setStyle(Qt.PenStyle.SolidLine)
        crosshair_pen.setCosmetic(True)

        for i, angle_deg in enumerate(angles):
            start_point, end_point = self.calculate_boundary_line_endpoints(scene_pos, angle_deg)

            diagonal_line = QGraphicsLineItem(start_point.x(), start_point.y(), end_point.x(), end_point.y())
            diagonal_line.setPen(crosshair_pen)
            diagonal_line.setZValue(999)
            self.scene().addItem(diagonal_line)
            self.crosshair_diagonals[i] = diagonal_line

        self.create_exiva_ranges(scene_pos, crosshair_width)

        base_square_size = 1.0
        square_size = base_square_size * max(1.0, min(2.0, 1.0 / self.zoom_factor)) if self.zoom_factor < 1.0 else base_square_size

        self.crosshair_center_square = QGraphicsRectItem(
            scene_pos.x() - square_size/2, scene_pos.y() - square_size/2,
            square_size, square_size
        )
        self.crosshair_center_square.setPen(crosshair_pen)
        self.crosshair_center_square.setBrush(QBrush(Qt.BrushStyle.NoBrush))
        self.crosshair_center_square.setZValue(1001)
        self.scene().addItem(self.crosshair_center_square)

        logger.info(f"Exiva crosshairs placed at ({scene_pos.x():.2f}, {scene_pos.y():.2f}) on floor {self.current_floor_id} with 8 diagonal boundary lines and distance ranges")

    def create_exiva_ranges(self, center_pos: QPointF, range_width: float):
        """Create Exiva spell distance range overlays."""
        crosshair_color = self.get_crosshair_color()
        range_pen = QPen(crosshair_color)
        range_pen.setWidthF(range_width)
        range_pen.setStyle(Qt.PenStyle.SolidLine)
        range_pen.setCosmetic(True)

        inner_size = self.calculate_exiva_range_size(100)

        self.crosshair_inner_range = QGraphicsRectItem(
            center_pos.x() - inner_size/2, center_pos.y() - inner_size/2,
            inner_size, inner_size
        )
        self.crosshair_inner_range.setPen(range_pen)
        self.crosshair_inner_range.setBrush(QBrush(Qt.BrushStyle.NoBrush))
        self.crosshair_inner_range.setZValue(998)
        self.scene().addItem(self.crosshair_inner_range)

        outer_size = self.calculate_exiva_range_size(250)

        self.crosshair_outer_range = QGraphicsRectItem(
            center_pos.x() - outer_size/2, center_pos.y() - outer_size/2,
            outer_size, outer_size
        )
        self.crosshair_outer_range.setPen(range_pen)
        self.crosshair_outer_range.setBrush(QBrush(Qt.BrushStyle.NoBrush))
        self.crosshair_outer_range.setZValue(997)
        self.scene().addItem(self.crosshair_outer_range)

    def update_crosshair_appearance(self):
        """Update the appearance of existing Exiva crosshairs based on current zoom level."""
        if not self.scene() or self.global_crosshair_position is None:
            return

        # Don't update crosshairs if they are hidden for area editing
        if self.crosshairs_hidden_for_area_editing:
            return

        has_diagonals = any(line is not None for line in self.crosshair_diagonals)
        if not (has_diagonals or self.crosshair_center_square):
            return

        crosshair_width = self.calculate_crosshair_pen_width(0.3)

        crosshair_color = self.get_crosshair_color()
        crosshair_pen = QPen(crosshair_color)
        crosshair_pen.setWidthF(crosshair_width)
        crosshair_pen.setStyle(Qt.PenStyle.SolidLine)
        crosshair_pen.setCosmetic(True)

        angles = self.get_exiva_diagonal_angles()
        scene_pos = self.global_crosshair_position

        for diagonal_line, angle_deg in zip(self.crosshair_diagonals, angles):
            if diagonal_line is not None:
                diagonal_line.setPen(crosshair_pen)
                start_point, end_point = self.calculate_boundary_line_endpoints(scene_pos, angle_deg)
                diagonal_line.setLine(start_point.x(), start_point.y(), end_point.x(), end_point.y())

        if self.crosshair_inner_range:
            self.crosshair_inner_range.setPen(crosshair_pen)

        if self.crosshair_outer_range:
            self.crosshair_outer_range.setPen(crosshair_pen)

        if self.crosshair_center_square:
            self.crosshair_center_square.setPen(crosshair_pen)

            base_square_size = 1.0
            square_size = base_square_size * max(1.0, min(2.0, 1.0 / self.zoom_factor)) if self.zoom_factor < 1.0 else base_square_size

            self.crosshair_center_square.setRect(
                scene_pos.x() - square_size/2, scene_pos.y() - square_size/2,
                square_size, square_size
            )

        logger.debug(f"Updated Exiva crosshair appearance: crosshair width {crosshair_width:.3f}, lines extend to scene boundaries")

    def remove_crosshairs(self):
        """Remove existing Exiva crosshairs from the scene."""
        for i in range(8):
            if self.crosshair_diagonals[i] and self.scene():
                try:
                    self.scene().removeItem(self.crosshair_diagonals[i])
                except RuntimeError:
                    pass
                self.crosshair_diagonals[i] = None

        if self.crosshair_center_square and self.scene():
            try:
                self.scene().removeItem(self.crosshair_center_square)
            except RuntimeError:
                pass
            self.crosshair_center_square = None

        if self.crosshair_inner_range and self.scene():
            try:
                self.scene().removeItem(self.crosshair_inner_range)
            except RuntimeError:
                pass
            self.crosshair_inner_range = None

        if self.crosshair_outer_range and self.scene():
            try:
                self.scene().removeItem(self.crosshair_outer_range)
            except RuntimeError:
                pass
            self.crosshair_outer_range = None

        self.global_crosshair_position = None

        # Reset area editing crosshair state
        self.crosshairs_hidden_for_area_editing = False
        self.saved_crosshair_position_for_area_editing = None

    def restore_crosshairs(self, _floor_id: int = None):
        """Restore global crosshairs if they exist."""
        if not self.scene():
            logger.warning("Cannot restore crosshairs - no scene available")
            return

        if self.global_crosshair_position is not None:
            # If in area editing mode, don't display crosshairs but ensure state is correct
            if self.area_editing_mode != AreaEditingMode.DISABLED:
                self.saved_crosshair_position_for_area_editing = QPointF(self.global_crosshair_position)
                self.crosshairs_hidden_for_area_editing = True
                logger.info(f"Crosshairs position preserved but hidden due to area editing mode on floor {_floor_id}")
            else:
                self.place_crosshairs(self.global_crosshair_position)

    def save_crosshair_position(self, _floor_id: int = None):
        """Save current global crosshair position (no-op since crosshairs are already global)."""
        pass

    def clear_all_crosshairs(self):
        """Clear global crosshairs."""
        self.remove_crosshairs()

    def hide_crosshairs_for_area_editing(self):
        """Hide crosshairs when entering area editing mode."""
        if self.global_crosshair_position is not None and not self.crosshairs_hidden_for_area_editing:
            # Save the current crosshair position
            self.saved_crosshair_position_for_area_editing = QPointF(self.global_crosshair_position)
            # Remove crosshairs from scene but don't clear the global position
            self._remove_crosshair_graphics_items()
            self.crosshairs_hidden_for_area_editing = True
            logger.info("Crosshairs hidden for area editing mode")

    def show_crosshairs_for_area_editing(self):
        """Show crosshairs when exiting area editing mode."""
        if self.crosshairs_hidden_for_area_editing and self.saved_crosshair_position_for_area_editing is not None:
            # Restore crosshairs at the saved position
            self.place_crosshairs(self.saved_crosshair_position_for_area_editing)
            self.crosshairs_hidden_for_area_editing = False
            self.saved_crosshair_position_for_area_editing = None
            logger.info("Crosshairs restored after area editing mode")

    def _remove_crosshair_graphics_items(self):
        """Remove crosshair graphics items from scene without clearing position state."""
        for i in range(8):
            if self.crosshair_diagonals[i] and self.scene():
                try:
                    self.scene().removeItem(self.crosshair_diagonals[i])
                except RuntimeError:
                    pass
                self.crosshair_diagonals[i] = None

        if self.crosshair_center_square and self.scene():
            try:
                self.scene().removeItem(self.crosshair_center_square)
            except RuntimeError:
                pass
            self.crosshair_center_square = None

        if self.crosshair_inner_range and self.scene():
            try:
                self.scene().removeItem(self.crosshair_inner_range)
            except RuntimeError:
                pass
            self.crosshair_inner_range = None

        if self.crosshair_outer_range and self.scene():
            try:
                self.scene().removeItem(self.crosshair_outer_range)
            except RuntimeError:
                pass
            self.crosshair_outer_range = None

    # Area editing methods
    def set_area_editing_mode(self, mode: AreaEditingMode):
        """Set the area editing mode."""
        previous_mode = self.area_editing_mode
        self.area_editing_mode = mode

        # Handle crosshair visibility based on mode change
        if previous_mode == AreaEditingMode.DISABLED and mode != AreaEditingMode.DISABLED:
            # Entering area editing mode - hide crosshairs
            self.hide_crosshairs_for_area_editing()
        elif previous_mode != AreaEditingMode.DISABLED and mode == AreaEditingMode.DISABLED:
            # Exiting area editing mode - show crosshairs
            self.show_crosshairs_for_area_editing()

        if mode == AreaEditingMode.DISABLED:
            self.finish_area_creation()
        logger.info(f"Area editing mode set to: {mode.value}")

    def handle_area_creation_click(self, scene_pos: QPointF):
        """Handle mouse clicks during area creation."""
        if self.area_editing_mode == AreaEditingMode.RECTANGLE:
            if not self.creating_area:
                # Start rectangle creation
                self.creating_area = True
                self.area_creation_points = [scene_pos]
                self.create_temp_area_preview()
            else:
                # Finish rectangle creation
                self.area_creation_points.append(scene_pos)
                self.finish_area_creation()

        elif self.area_editing_mode == AreaEditingMode.POLYGON:
            if not self.creating_area:
                # Start polygon creation
                self.creating_area = True
                self.area_creation_points = [scene_pos]
                self.create_temp_area_preview()
            else:
                # Add point to polygon
                self.area_creation_points.append(scene_pos)
                self.update_temp_area_preview()

    def create_temp_area_preview(self):
        """Create a temporary preview of the area being created."""
        if self.temp_area_item:
            self.scene().removeItem(self.temp_area_item)

        polygon = QPolygonF()
        for point in self.area_creation_points:
            polygon.append(point)

        self.temp_area_item = QGraphicsPolygonItem(polygon)
        pen = QPen(QColor("#FF0000"))
        pen.setWidthF(2.0)
        pen.setStyle(Qt.PenStyle.DashLine)
        pen.setCosmetic(True)
        self.temp_area_item.setPen(pen)
        self.temp_area_item.setBrush(QBrush(QColor(255, 0, 0, 50)))
        self.temp_area_item.setZValue(600)
        self.scene().addItem(self.temp_area_item)

    def update_temp_area_preview(self):
        """Update the temporary area preview."""
        if self.temp_area_item and self.area_creation_points:
            polygon = QPolygonF()
            for point in self.area_creation_points:
                polygon.append(point)
            self.temp_area_item.setPolygon(polygon)

    def finish_area_creation(self):
        """Finish creating an area and add it to the data."""
        if not self.creating_area or len(self.area_creation_points) < 2:
            self.cancel_area_creation()
            return

        # Create area data
        area_id = str(uuid.uuid4())
        coordinates = [(point.x(), point.y()) for point in self.area_creation_points]

        # For rectangles, ensure we have 4 points
        if self.area_editing_mode == AreaEditingMode.RECTANGLE and len(coordinates) == 2:
            x1, y1 = coordinates[0]
            x2, y2 = coordinates[1]
            coordinates = [
                (min(x1, x2), min(y1, y2)),
                (max(x1, x2), min(y1, y2)),
                (max(x1, x2), max(y1, y2)),
                (min(x1, x2), max(y1, y2))
            ]

        import time
        default_colors = self.area_data_manager.get_default_colors()
        default_difficulty = MonsterDifficulty.MEDIUM.value
        default_color = default_colors[default_difficulty]

        area_data = AreaData(
            area_id=area_id,
            floor=self.current_floor_id,
            name=f"Area {len(self.area_graphics_items) + 1}",
            coordinates=coordinates,
            area_type=self.area_editing_mode.value,
            difficulty_levels=[default_difficulty],
            color=default_color,
            transparency=0.3,
            metadata={},
            created_timestamp=time.time(),
            modified_timestamp=time.time()
        )

        # Add to data manager
        self.area_data_manager.add_area(area_data)

        # Create graphics item
        self.add_area_graphics_item(area_data)

        # Emit signal
        self.areaCreated.emit(area_data)

        # Clean up
        self.cancel_area_creation()
        logger.info(f"Created new area: {area_data.name} on floor {area_data.floor}")

    def cancel_area_creation(self):
        """Cancel area creation and clean up."""
        self.creating_area = False
        self.area_creation_points = []

        if self.temp_area_item:
            self.scene().removeItem(self.temp_area_item)
            self.temp_area_item = None

    def add_area_graphics_item(self, area_data: AreaData):
        """Add a graphics item for an area."""
        graphics_item = AreaGraphicsItem(area_data)
        self.area_graphics_items[area_data.area_id] = graphics_item
        self.scene().addItem(graphics_item)

    def remove_area_graphics_item(self, area_id: str):
        """Remove a graphics item for an area."""
        if area_id in self.area_graphics_items:
            graphics_item = self.area_graphics_items[area_id]
            self.scene().removeItem(graphics_item)
            del self.area_graphics_items[area_id]

    def select_area(self, area_id: str):
        """Select an area."""
        # Deselect previous area
        if self.selected_area_id and self.selected_area_id in self.area_graphics_items:
            self.area_graphics_items[self.selected_area_id].set_selected_area(False)

        # Select new area
        self.selected_area_id = area_id
        if area_id in self.area_graphics_items:
            self.area_graphics_items[area_id].set_selected_area(True)
            self.areaSelected.emit(area_id)
            logger.info(f"Selected area: {area_id}")

    def deselect_area(self):
        """Deselect the currently selected area."""
        if self.selected_area_id and self.selected_area_id in self.area_graphics_items:
            self.area_graphics_items[self.selected_area_id].set_selected_area(False)
            logger.info(f"Deselected area: {self.selected_area_id}")

        self.selected_area_id = None
        # Emit signal with empty area_id to indicate deselection
        self.areaSelected.emit("")

    def load_areas_for_floor(self, floor: int):
        """Load and display areas for a specific floor."""
        # Clear existing area graphics (only if they haven't been cleared already)
        # This handles both normal area loading and floor switches where scene.clear() was called
        for graphics_item in list(self.area_graphics_items.values()):
            try:
                if self.scene() and graphics_item.scene() == self.scene():
                    self.scene().removeItem(graphics_item)
            except RuntimeError:
                # Graphics item was already deleted (e.g., by scene.clear())
                pass
        self.area_graphics_items.clear()
        self.selected_area_id = None

        # Load areas for this floor
        areas = self.area_data_manager.get_areas_for_floor(floor)
        for area_data in areas:
            self.add_area_graphics_item(area_data)


class AreaPropertiesPanel(QWidget):
    """Properties panel for editing area attributes."""

    areaUpdated = pyqtSignal(AreaData)
    areaDeleted = pyqtSignal(str)  # area_id

    def __init__(self, area_data_manager: AreaDataManager, parent=None):
        super().__init__(parent)
        self.area_data_manager = area_data_manager
        self.current_area: Optional[AreaData] = None
        self.updating_ui = False

        self.setup_ui()
        self.setEnabled(False)  # Disabled until an area is selected

    def setup_ui(self):
        """Set up the properties panel UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Title
        title_label = QLabel("Area Properties")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(title_label)

        # Scroll area for properties
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        properties_widget = QWidget()
        properties_layout = QVBoxLayout(properties_widget)
        properties_layout.setContentsMargins(5, 5, 5, 5)
        properties_layout.setSpacing(15)

        # Basic Information Group
        basic_group = QGroupBox("Basic Information")
        basic_layout = QFormLayout(basic_group)

        self.name_edit = QLineEdit()
        self.name_edit.textChanged.connect(self.on_name_changed)
        basic_layout.addRow("Name:", self.name_edit)

        self.area_type_label = QLabel()
        basic_layout.addRow("Type:", self.area_type_label)

        properties_layout.addWidget(basic_group)

        # Monster Difficulty Group
        difficulty_group = QGroupBox("Monster Difficulty")
        difficulty_layout = QVBoxLayout(difficulty_group)

        self.difficulty_checkboxes = {}
        for difficulty in MonsterDifficulty:
            checkbox = QCheckBox(difficulty.value.title())
            checkbox.stateChanged.connect(self.on_difficulty_changed)
            self.difficulty_checkboxes[difficulty.value] = checkbox
            difficulty_layout.addWidget(checkbox)

        properties_layout.addWidget(difficulty_group)

        # Visual Properties Group
        visual_group = QGroupBox("Visual Properties")
        visual_layout = QFormLayout(visual_group)

        # Color picker
        self.color_button = QPushButton()
        self.color_button.setFixedHeight(30)
        self.color_button.clicked.connect(self.on_color_clicked)
        visual_layout.addRow("Color:", self.color_button)

        # Transparency slider
        self.transparency_slider = QSlider(Qt.Orientation.Horizontal)
        self.transparency_slider.setRange(0, 100)
        self.transparency_slider.setValue(30)
        self.transparency_slider.valueChanged.connect(self.on_transparency_changed)

        self.transparency_label = QLabel("30%")
        transparency_widget = QWidget()
        transparency_layout = QHBoxLayout(transparency_widget)
        transparency_layout.setContentsMargins(0, 0, 0, 0)
        transparency_layout.addWidget(self.transparency_slider)
        transparency_layout.addWidget(self.transparency_label)

        visual_layout.addRow("Transparency:", transparency_widget)

        properties_layout.addWidget(visual_group)

        # Action buttons
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 0, 0, 0)

        self.delete_button = QPushButton("Delete Area")
        self.delete_button.setStyleSheet("background-color: #ff4444; color: white;")
        self.delete_button.clicked.connect(self.on_delete_clicked)
        button_layout.addWidget(self.delete_button)

        properties_layout.addWidget(button_widget)

        # Add stretch to push everything to the top
        properties_layout.addStretch()

        scroll_area.setWidget(properties_widget)
        layout.addWidget(scroll_area)

    def set_area(self, area_data: Optional[AreaData]):
        """Set the area to edit."""
        self.current_area = area_data
        self.setEnabled(area_data is not None)

        if area_data:
            self.updating_ui = True

            # Update basic information
            self.name_edit.setText(area_data.name)
            self.area_type_label.setText(area_data.area_type.title())

            # Update difficulty checkboxes
            for difficulty_value, checkbox in self.difficulty_checkboxes.items():
                checkbox.setChecked(difficulty_value in area_data.difficulty_levels)

            # Update color
            self.update_color_button(area_data.color)

            # Update transparency
            transparency_percent = int(area_data.transparency * 100)
            self.transparency_slider.setValue(transparency_percent)
            self.transparency_label.setText(f"{transparency_percent}%")

            self.updating_ui = False

    def update_color_button(self, color_hex: str):
        """Update the color button appearance."""
        self.color_button.setStyleSheet(f"background-color: {color_hex}; border: 1px solid #ccc;")
        self.color_button.setText(color_hex)

    def on_name_changed(self):
        """Handle name change."""
        if self.updating_ui or not self.current_area:
            return

        self.current_area.name = self.name_edit.text()
        self.current_area.modified_timestamp = time.time()
        self.save_and_emit_update()

    def on_difficulty_changed(self):
        """Handle difficulty level change."""
        if self.updating_ui or not self.current_area:
            return

        selected_difficulties = []
        for difficulty_value, checkbox in self.difficulty_checkboxes.items():
            if checkbox.isChecked():
                selected_difficulties.append(difficulty_value)

        self.current_area.difficulty_levels = selected_difficulties

        # Auto-update color to match primary difficulty if only one is selected
        if len(selected_difficulties) == 1:
            default_colors = self.area_data_manager.get_default_colors()
            new_color = default_colors[selected_difficulties[0]]
            self.current_area.color = new_color
            self.update_color_button(new_color)

        self.current_area.modified_timestamp = time.time()
        self.save_and_emit_update()

    def on_color_clicked(self):
        """Handle color picker button click."""
        if not self.current_area:
            return

        current_color = QColor(self.current_area.color)
        color = QColorDialog.getColor(current_color, self, "Select Area Color")

        if color.isValid():
            color_hex = color.name()
            self.current_area.color = color_hex
            self.current_area.modified_timestamp = time.time()
            self.update_color_button(color_hex)
            self.save_and_emit_update()

    def on_transparency_changed(self, value: int):
        """Handle transparency slider change."""
        if self.updating_ui or not self.current_area:
            return

        transparency = value / 100.0
        self.current_area.transparency = transparency
        self.current_area.modified_timestamp = time.time()
        self.transparency_label.setText(f"{value}%")
        self.save_and_emit_update()

    def on_delete_clicked(self):
        """Handle delete button click."""
        if not self.current_area:
            return

        reply = QMessageBox.question(
            self, "Delete Area",
            f"Are you sure you want to delete '{self.current_area.name}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            area_id = self.current_area.area_id
            self.area_data_manager.remove_area(self.current_area.floor, area_id)
            self.areaDeleted.emit(area_id)
            self.set_area(None)

    def save_and_emit_update(self):
        """Save the area and emit update signal."""
        if self.current_area:
            self.area_data_manager.update_area(self.current_area)
            self.areaUpdated.emit(self.current_area)


class MinimapViewer(QWidget):
    """Main minimap viewer widget with floor navigation and zoom controls."""

    floorChanged = pyqtSignal(int)

    def __init__(self, minimap_dir: str = "processed_minimap", parent=None):
        super().__init__(parent)

        self.minimap_dir = Path(minimap_dir)
        self.current_floor = 7
        self.floor_images: Dict[int, QPixmap] = {}

        self.floor_order = [
            0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15
        ]

        self.setup_ui()
        self.load_floor_images()
        self.set_floor(self.current_floor)
    
    def setup_ui(self):
        """Set up the user interface with vertical layout and sidebars."""
        # Main horizontal layout
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # Left sidebar for controls
        left_sidebar = QFrame()
        left_sidebar.setFrameStyle(QFrame.Shape.StyledPanel)
        left_sidebar.setFixedWidth(200)
        left_sidebar.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Expanding)

        left_layout = QVBoxLayout(left_sidebar)
        left_layout.setContentsMargins(10, 10, 10, 10)
        left_layout.setSpacing(10)

        # Floor controls section
        floor_group = QFrame()
        floor_group.setFrameStyle(QFrame.Shape.Box)
        floor_group_layout = QVBoxLayout(floor_group)
        floor_group_layout.setContentsMargins(8, 8, 8, 8)
        floor_group_layout.setSpacing(5)

        floor_title = QLabel("Floor Navigation")
        floor_title.setStyleSheet("font-weight: bold; font-size: 12px;")
        floor_group_layout.addWidget(floor_title)

        floor_label = QLabel("Floor:")
        floor_group_layout.addWidget(floor_label)

        # Floor navigation buttons and combo
        floor_nav_layout = QHBoxLayout()
        floor_nav_layout.setSpacing(8)  # Add proper spacing between elements

        self.floor_down_btn = QPushButton("-")
        self.floor_down_btn.setMaximumWidth(30)
        self.floor_down_btn.setToolTip("Go down one floor")
        self.floor_down_btn.clicked.connect(self.floor_down)

        self.floor_combo = QComboBox()
        self.floor_combo.setMinimumWidth(100)
        self.floor_combo.currentTextChanged.connect(self.on_floor_changed)

        self.floor_up_btn = QPushButton("+")
        self.floor_up_btn.setMaximumWidth(30)
        self.floor_up_btn.setToolTip("Go up one floor")
        self.floor_up_btn.clicked.connect(self.floor_up)

        floor_nav_layout.addWidget(self.floor_down_btn)
        floor_nav_layout.addWidget(self.floor_combo)
        floor_nav_layout.addWidget(self.floor_up_btn)
        floor_group_layout.addLayout(floor_nav_layout)

        # Area editing controls section
        area_group = QFrame()
        area_group.setFrameStyle(QFrame.Shape.Box)
        area_group_layout = QVBoxLayout(area_group)
        area_group_layout.setContentsMargins(8, 8, 8, 8)
        area_group_layout.setSpacing(5)

        area_title = QLabel("Area Editing")
        area_title.setStyleSheet("font-weight: bold; font-size: 12px;")
        area_group_layout.addWidget(area_title)

        # Area editing mode buttons
        self.area_mode_disabled_btn = QPushButton("Disabled")
        self.area_mode_disabled_btn.setCheckable(True)
        self.area_mode_disabled_btn.setChecked(True)
        self.area_mode_disabled_btn.clicked.connect(lambda: self.set_area_editing_mode(AreaEditingMode.DISABLED))
        area_group_layout.addWidget(self.area_mode_disabled_btn)

        self.area_mode_rectangle_btn = QPushButton("Rectangle")
        self.area_mode_rectangle_btn.setCheckable(True)
        self.area_mode_rectangle_btn.clicked.connect(lambda: self.set_area_editing_mode(AreaEditingMode.RECTANGLE))
        area_group_layout.addWidget(self.area_mode_rectangle_btn)

        self.area_mode_polygon_btn = QPushButton("Polygon")
        self.area_mode_polygon_btn.setCheckable(True)
        self.area_mode_polygon_btn.clicked.connect(lambda: self.set_area_editing_mode(AreaEditingMode.POLYGON))
        area_group_layout.addWidget(self.area_mode_polygon_btn)

        # Group the mode buttons
        self.area_mode_buttons = [
            self.area_mode_disabled_btn,
            self.area_mode_rectangle_btn,
            self.area_mode_polygon_btn
        ]

        # Add groups to left sidebar
        left_layout.addWidget(floor_group)
        left_layout.addWidget(area_group)
        left_layout.addStretch()  # Push content to top

        # Center area for main content
        center_frame = QFrame()
        center_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        center_layout = QVBoxLayout(center_frame)
        center_layout.setContentsMargins(0, 0, 0, 0)

        self.graphics_view = MinimapGraphicsView()
        self.graphics_scene = QGraphicsScene()
        self.graphics_view.setScene(self.graphics_scene)

        # Connect area editing signals
        self.graphics_view.areaCreated.connect(self.on_area_created)
        self.graphics_view.areaSelected.connect(self.on_area_selected)

        center_layout.addWidget(self.graphics_view)

        # Right sidebar for area properties
        right_sidebar = QFrame()
        right_sidebar.setFrameStyle(QFrame.Shape.StyledPanel)
        right_sidebar.setFixedWidth(250)
        right_sidebar.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Expanding)

        right_layout = QVBoxLayout(right_sidebar)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(0)

        # Area properties panel
        self.area_properties_panel = AreaPropertiesPanel(self.graphics_view.area_data_manager)
        self.area_properties_panel.areaUpdated.connect(self.on_area_updated)
        self.area_properties_panel.areaDeleted.connect(self.on_area_deleted)
        right_layout.addWidget(self.area_properties_panel)

        # Add all sections to main layout
        main_layout.addWidget(left_sidebar)
        main_layout.addWidget(center_frame, 1)  # Center gets all extra space
        main_layout.addWidget(right_sidebar)

        self.current_minimap_item: Optional[QGraphicsPixmapItem] = None

        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

    def load_floor_images(self):
        """Load all available floor images."""
        logger.info(f"Loading floor images from {self.minimap_dir}")

        if not self.minimap_dir.exists():
            logger.error(f"Minimap directory not found: {self.minimap_dir}")
            return

        loaded_floors = []
        for floor in self.floor_order:
            floor_file = self.minimap_dir / f"floor_{floor:02d}.png"
            if floor_file.exists():
                try:
                    pixmap = QPixmap(str(floor_file))
                    if not pixmap.isNull():
                        self.floor_images[floor] = pixmap
                        loaded_floors.append(floor)
                    else:
                        logger.warning(f"Failed to load floor image: {floor_file}")
                except Exception as e:
                    logger.error(f"Error loading floor {floor}: {e}")

        self.floor_combo.blockSignals(True)
        self.floor_combo.clear()
        for floor in self.floor_order:
            if floor in self.floor_images:
                if floor == 7:
                    self.floor_combo.addItem(f"Floor {floor:02d} (Main)", floor)
                else:
                    self.floor_combo.addItem(f"Floor {floor:02d}", floor)
        self.floor_combo.blockSignals(False)

        logger.info(f"Loaded {len(loaded_floors)} floor images: {loaded_floors}")

    def set_floor(self, floor: int):
        """Set the current floor and display its minimap."""
        if floor not in self.floor_images:
            logger.warning(f"Floor {floor} not available")
            return

        # Preserve area editing mode before floor switch
        current_area_editing_mode = self.graphics_view.area_editing_mode

        # Clean up any in-progress area creation before clearing the scene
        # This prevents crashes when temp_area_item gets removed from scene
        if self.graphics_view.creating_area:
            logger.info("Canceling in-progress area creation due to floor switch")
            self.graphics_view.cancel_area_creation()

        # Clear area graphics items dictionary before clearing scene
        # This prevents crashes when scene.clear() deletes the graphics items
        self.graphics_view.area_graphics_items.clear()
        self.graphics_view.selected_area_id = None

        self.current_floor = floor
        self.graphics_view.current_floor_id = floor

        self.graphics_scene.clear()

        self.graphics_view.crosshair_diagonals = [None] * 8
        self.graphics_view.crosshair_center_square = None
        self.graphics_view.crosshair_inner_range = None
        self.graphics_view.crosshair_outer_range = None

        current_zoom = self.graphics_view.zoom_factor
        current_center = self.graphics_view.mapToScene(self.graphics_view.viewport().rect().center())

        pixmap = self.floor_images[floor]
        self.current_minimap_item = QGraphicsPixmapItem(pixmap)
        self.graphics_scene.addItem(self.current_minimap_item)

        new_scene_rect = self.current_minimap_item.boundingRect()
        self.graphics_scene.setSceneRect(new_scene_rect)

        logger.info(f"FLOOR_CHANGE - Floor {floor}: Scene rect {new_scene_rect.width():.0f}x{new_scene_rect.height():.0f}")

        if hasattr(self, '_first_floor_loaded'):
            self.graphics_view.zoom_to_factor(current_zoom)

            viewport_rect = self.graphics_view.viewport().rect()
            target_viewport_center = self.graphics_view.mapFromScene(current_center)
            current_viewport_center = viewport_rect.center()
            offset_x = target_viewport_center.x() - current_viewport_center.x()
            offset_y = target_viewport_center.y() - current_viewport_center.y()

            h_scroll = self.graphics_view.horizontalScrollBar()
            v_scroll = self.graphics_view.verticalScrollBar()
            h_scroll.setValue(h_scroll.value() + int(offset_x))
            v_scroll.setValue(v_scroll.value() + int(offset_y))

            logger.info(f"GLOBAL CAMERA - Floor {floor}: Zoom: {current_zoom:.4f}, Position: ({current_center.x():.2f}, {current_center.y():.2f}) - UNCHANGED")
        else:
            self.fit_view()
            self._first_floor_loaded = True

        self.graphics_view.restore_crosshairs(floor)

        # Load areas for this floor
        self.graphics_view.load_areas_for_floor(floor)

        # Restore area editing mode after floor switch
        # This preserves the user's editing mode selection across floor changes
        self.graphics_view.set_area_editing_mode(current_area_editing_mode)

        self.floor_combo.blockSignals(True)
        for i in range(self.floor_combo.count()):
            if self.floor_combo.itemData(i) == floor:
                self.floor_combo.setCurrentIndex(i)
                break
        self.floor_combo.blockSignals(False)

        logger.info(f"Switched to floor {floor}")
        self.floorChanged.emit(floor)

    def on_floor_changed(self, _floor_text: str):
        """Handle floor selection change from combo box."""
        current_data = self.floor_combo.currentData()
        if current_data is not None:
            self.set_floor(current_data)

    def floor_up(self):
        """Navigate to the next floor up (lower floor number)."""
        current_index = self.floor_order.index(self.current_floor)
        if current_index > 0:
            next_floor = self.floor_order[current_index - 1]
            if next_floor in self.floor_images:
                self.set_floor(next_floor)

    def floor_down(self):
        """Navigate to the next floor down (higher floor number)."""
        current_index = self.floor_order.index(self.current_floor)
        if current_index < len(self.floor_order) - 1:
            next_floor = self.floor_order[current_index + 1]
            if next_floor in self.floor_images:
                self.set_floor(next_floor)

    def keyPressEvent(self, event: QKeyEvent):
        """Handle keyboard shortcuts."""
        if event.key() == Qt.Key.Key_Plus or event.key() == Qt.Key.Key_Equal:
            self.floor_up()
        elif event.key() == Qt.Key.Key_Minus:
            self.floor_down()
        elif event.key() == Qt.Key.Key_C and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
            self.clear_crosshairs()
        else:
            super().keyPressEvent(event)

    def clear_crosshairs(self):
        """Clear global crosshairs."""
        self.graphics_view.clear_all_crosshairs()
        logger.info("Cleared global crosshairs")

    def fit_view(self):
        """Fit the minimap in the view."""
        self.graphics_view.fit_in_view_with_margin(0.05)

    def get_current_floor(self) -> int:
        """Get the currently displayed floor."""
        return self.current_floor

    def get_available_floors(self) -> list[int]:
        """Get list of available floors."""
        return list(self.floor_images.keys())

    def get_camera_info(self) -> dict:
        """Get current camera information."""
        center = self.graphics_view.mapToScene(self.graphics_view.viewport().rect().center())
        return {
            'floor': self.current_floor,
            'zoom_factor': self.graphics_view.zoom_factor,
            'center_x': center.x(),
            'center_y': center.y()
        }

    # Area editing methods
    def set_area_editing_mode(self, mode: AreaEditingMode):
        """Set the area editing mode and update UI."""
        self.graphics_view.set_area_editing_mode(mode)

        # Update button states
        for button in self.area_mode_buttons:
            button.setChecked(False)

        if mode == AreaEditingMode.DISABLED:
            self.area_mode_disabled_btn.setChecked(True)
        elif mode == AreaEditingMode.RECTANGLE:
            self.area_mode_rectangle_btn.setChecked(True)
        elif mode == AreaEditingMode.POLYGON:
            self.area_mode_polygon_btn.setChecked(True)

    def on_area_created(self, area_data: AreaData):
        """Handle area creation."""
        logger.info(f"Area created: {area_data.name} on floor {area_data.floor}")

    def on_area_selected(self, area_id: str):
        """Handle area selection and deselection."""
        if area_id:
            # Area selected
            area_data = self.graphics_view.area_data_manager.get_area_by_id(self.current_floor, area_id)
            self.area_properties_panel.set_area(area_data)
        else:
            # Area deselected (empty area_id)
            self.area_properties_panel.set_area(None)

    def on_area_updated(self, area_data: AreaData):
        """Handle area property updates."""
        if area_data.area_id in self.graphics_view.area_graphics_items:
            graphics_item = self.graphics_view.area_graphics_items[area_data.area_id]
            graphics_item.update_from_area_data(area_data)
        logger.info(f"Area updated: {area_data.name}")

    def on_area_deleted(self, area_id: str):
        """Handle area deletion."""
        self.graphics_view.remove_area_graphics_item(area_id)
        if self.graphics_view.selected_area_id == area_id:
            self.graphics_view.selected_area_id = None
        logger.info(f"Area deleted: {area_id}")


class MinimapViewerWindow(QMainWindow):
    """Main window for the minimap viewer application."""

    def __init__(self, minimap_dir: str = "processed_minimap"):
        super().__init__()

        self.setWindowTitle("FiendishFinder - Minimap Viewer")
        self.setGeometry(100, 100, 1200, 800)

        self.minimap_viewer = MinimapViewer(minimap_dir)
        self.setCentralWidget(self.minimap_viewer)

        self.minimap_viewer.floorChanged.connect(self.on_floor_changed)

        self.status_bar = self.statusBar()
        self.update_status()

    def on_floor_changed(self, _floor: int):
        """Handle floor change events."""
        self.update_status()

    def update_status(self):
        """Update status bar with current information."""
        camera_info = self.minimap_viewer.get_camera_info()
        available_floors = len(self.minimap_viewer.get_available_floors())

        status_text = (
            f"Floor: {camera_info['floor']:02d} | "
            f"Zoom: {camera_info['zoom_factor']:.1f}x | "
            f"Position: ({camera_info['center_x']:.0f}, {camera_info['center_y']:.0f}) | "
            f"Available Floors: {available_floors}"
        )

        self.status_bar.showMessage(status_text)


def main():
    """Main function to run the minimap viewer application."""
    app = QApplication(sys.argv)

    app.setApplicationName("FiendishFinder Minimap Viewer")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("FiendishFinder")

    window = MinimapViewerWindow()

    minimap_dir = Path("processed_minimap")
    if not minimap_dir.exists():
        QMessageBox.warning(
            window,
            "Directory Not Found",
            f"Minimap directory '{minimap_dir}' not found.\n"
            "Please ensure the processed minimap images are available."
        )

    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
